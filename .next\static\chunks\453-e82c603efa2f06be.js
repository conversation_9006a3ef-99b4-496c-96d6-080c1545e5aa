"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[453],{8453:(e,t,r)=>{async function s(e){if(!("object"!=typeof e||!e)){if("object"==typeof e&&e&&"headers"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json&&"text"in e&&"function"==typeof e.json){let t=e.headers.get("Content-Type")?.toLowerCase();if(t?.startsWith("application/json")||t?.startsWith("application/health+json")){let t=await e.json();if(!e.ok||"errors"in t)throw t;return"data"in t?t.data:t}if(t?.startsWith("text/html")||t?.startsWith("text/plain")){let t=await e.text();if(!e.ok)throw t;return t}return 204===e.status?null:e}if("errors"in e)throw e;return"data"in e?e.data:e}}r.d(t,{F1f:()=>c,ieq:()=>n,k0O:()=>f,zs8:()=>h});var i=async(e,t,r=globalThis.fetch)=>(t.headers="object"!=typeof t.headers||Array.isArray(t.headers)?{}:t.headers,r(e,t).then(e=>s(e).catch(t=>{let r={errors:t&&"object"==typeof t&&"errors"in t?t.errors:t,response:e};return t&&"object"==typeof t&&"data"in t&&(r.data=t.data),Promise.reject(r)}))),o={fetch:globalThis.fetch,WebSocket:globalThis.WebSocket,URL:globalThis.URL,logger:globalThis.console},n=(e,t={})=>{let r=t.globals?{...o,...t.globals}:o;return{globals:r,url:new r.URL(e),with(e){return{...this,...e(this)}}}},a=(e,t)=>{if(0===e.length)throw Error(t)},l=(e,t)=>{if(["directus_access","directus_activity","directus_collections","directus_comments","directus_fields","directus_files","directus_folders","directus_migrations","directus_permissions","directus_policies","directus_presets","directus_relations","directus_revisions","directus_roles","directus_sessions","directus_settings","directus_users","directus_webhooks","directus_dashboards","directus_panels","directus_notifications","directus_shares","directus_flows","directus_operations","directus_translations","directus_versions","directus_extensions"].includes(String(e)))throw Error(t)},c=(e,t)=>()=>(a(String(e),"Collection cannot be empty"),l(e,"Cannot use readItems for core collections"),{path:`/items/${e}`,params:t??{},method:"GET"}),f=(e,t,r)=>()=>(a(String(e),"Collection cannot be empty"),l(e,"Cannot use readItem for core collections"),a(String(t),"Key cannot be empty"),{path:`/items/${e}/${t}`,params:r??{},method:"GET"}),d={},h=(e={})=>t=>{let r={...d,...e};return{async request(s){let o=s();if(o.headers||(o.headers={}),"Content-Type"in o.headers?"multipart/form-data"===o.headers["Content-Type"]&&delete o.headers["Content-Type"]:o.headers["Content-Type"]="application/json","getToken"in this&&!("Authorization"in o.headers)){let e=await this.getToken();e&&(o.headers.Authorization=`Bearer ${e}`)}let n=((e,t,r)=>{let s,i,o=new globalThis.URL("/"===e.pathname?t:(s=e.pathname,i=t,s.endsWith("/")&&(s=s.slice(0,-1)),i.startsWith("/")||(i="/"+i),s+i),e);if(r)for(let[e,t]of Object.entries((e=>{let t={};for(let[r,s]of(Array.isArray(e.fields)&&e.fields.length>0&&(t.fields=(e=>{let t=(e,r=[])=>{if("object"==typeof e){let s=[];for(let i in e){let o=e[i]??[];if(Array.isArray(o))for(let e of o)s.push(t(e,[...r,i]));else if("object"==typeof o)for(let e of Object.keys(o))for(let n of o[e])s.push(t(n,[...r,`${i}:${e}`]))}return s.flatMap(e=>e)}return[...r,String(e)].join(".")};return e.flatMap(e=>t(e))})(e.fields).join(",")),e.filter&&Object.keys(e.filter).length>0&&(t.filter=JSON.stringify(e.filter)),e.search&&(t.search=e.search),"sort"in e&&e.sort&&(t.sort="string"==typeof e.sort?e.sort:e.sort.join(",")),"number"==typeof e.limit&&e.limit>=-1&&(t.limit=String(e.limit)),"number"==typeof e.offset&&e.offset>=0&&(t.offset=String(e.offset)),"number"==typeof e.page&&e.page>=1&&(t.page=String(e.page)),e.deep&&Object.keys(e.deep).length>0&&(t.deep=JSON.stringify(e.deep)),e.alias&&Object.keys(e.alias).length>0&&(t.alias=JSON.stringify(e.alias)),e.aggregate&&Object.keys(e.aggregate).length>0&&(t.aggregate=JSON.stringify(e.aggregate)),e.groupBy&&e.groupBy.length>0&&(t.groupBy=e.groupBy.join(",")),Object.entries(e)))r in t||("string"==typeof s||"number"==typeof s||"boolean"==typeof s?t[r]=String(s):t[r]=JSON.stringify(s));return t})(r)))if(t&&"object"==typeof t&&!Array.isArray(t))for(let[r,s]of Object.entries(t))o.searchParams.set(`${e}[${r}]`,String(s));else o.searchParams.set(e,t);return o})(t.url,o.path,o.params),a={method:o.method??"GET",headers:o.headers??{}};"credentials"in r&&(a.credentials=r.credentials),o.body&&(a.body=o.body),o.onRequest&&(a=await o.onRequest(a)),r.onRequest&&(a=await r.onRequest(a));let l=await i(n.toString(),a,t.globals.fetch);return"onResponse"in o&&(l=await o.onResponse(l,a)),"onResponse"in e&&(l=await e.onResponse(l,a)),l}}}}}]);