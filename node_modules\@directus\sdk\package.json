{"name": "@directus/sdk", "version": "20.0.0", "description": "Directus JavaScript SDK", "homepage": "https://directus.io", "repository": {"type": "git", "url": "https://github.com/directus/directus.git", "directory": "sdk"}, "funding": "https://github.com/directus/directus?sponsor=1", "license": "MIT", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json"}, "main": "./dist/index.js", "files": ["dist"], "devDependencies": {"@directus/tsconfig": "3.0.0", "esbuild-plugin-replace": "1.4.0", "tsup": "8.4.0", "typescript": "5.8.2", "vitest": "2.1.9", "@directus/system-data": "3.1.1"}, "engines": {"node": ">=22"}, "scripts": {"build": "NODE_ENV=production tsup", "dev": "NODE_ENV=development tsup", "test": "vitest --typecheck --watch=false"}}