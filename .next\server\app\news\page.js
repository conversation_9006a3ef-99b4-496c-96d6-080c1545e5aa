(()=>{var a={};a.id=324,a.ids=[324],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},270:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(687),e=c(3210);c(9128);var f=c(6735),g=c(8899),h=c(6541),i=c(5093);function j(){let[a,b]=(0,e.useState)([]),[c,j]=(0,e.useState)([]),[k,l]=(0,e.useState)(null),[m,n]=(0,e.useState)(1),[o,p]=(0,e.useState)(1),[q,r]=(0,e.useState)(!0),{config:s}=(0,i.U)();return q?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white shadow-sm",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"新闻中心"}),(0,d.jsx)("p",{className:"mt-2 text-gray-600",children:"了解我们的最新资讯和行业动态"})]})}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)(g.A,{categories:c,selectedCategory:k,onCategorySelect:l,title:"新闻分类",allItemsLabel:"全部新闻"}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,d.jsx)("div",{className:"hidden lg:block lg:w-1/4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 sticky top-24",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"新闻分类"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>l(null),className:`w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ${null===k?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:"全部新闻"})}),c.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>l(a.id),className:`w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ${k===a.id?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:a.name})},a.id))]})]})}),(0,d.jsx)("div",{className:"lg:w-3/4",children:0===a.length?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsx)("p",{className:"text-gray-500 text-lg",children:"暂无新闻"})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:a.map(a=>(0,d.jsx)(f.A,{article:a},a.id))}),(0,d.jsx)(h.A,{currentPage:m,totalPages:o,onPageChange:a=>{n(a),window.scrollTo({top:0,behavior:"smooth"})}})]})})]})]})]})}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3260:(a,b,c)=>{Promise.resolve().then(c.bind(c,270))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},5116:(a,b,c)=>{Promise.resolve().then(c.bind(c,6976))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6541:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(687),e=c(2688);let f=(0,e.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),g=(0,e.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),h=({currentPage:a,totalPages:b,onPageChange:c})=>b<=1?null:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-8",children:[(0,d.jsxs)("button",{onClick:()=>c(a-1),disabled:1===a,className:`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${1===a?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-blue-600 hover:bg-blue-50"}`,children:[(0,d.jsx)(f,{className:"h-4 w-4 mr-1"}),"上一页"]}),(0,d.jsx)("div",{className:"flex items-center space-x-1",children:(()=>{let c=[];if(b<=5)for(let a=1;a<=b;a++)c.push(a);else if(a<=3){for(let a=1;a<=4;a++)c.push(a);c.push("..."),c.push(b)}else if(a>=b-2){c.push(1),c.push("...");for(let a=b-3;a<=b;a++)c.push(a)}else{c.push(1),c.push("...");for(let b=a-1;b<=a+1;b++)c.push(b);c.push("..."),c.push(b)}return c})().map((b,e)=>(0,d.jsx)("div",{children:"..."===b?(0,d.jsx)("span",{className:"px-3 py-2 text-gray-400",children:"..."}):(0,d.jsx)("button",{onClick:()=>c(b),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${a===b?"bg-blue-600 text-white":"text-gray-700 hover:text-blue-600 hover:bg-blue-50"}`,children:b})},e))}),(0,d.jsxs)("button",{onClick:()=>c(a+1),disabled:a===b,className:`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${a===b?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-blue-600 hover:bg-blue-50"}`,children:["下一页",(0,d.jsx)(g,{className:"h-4 w-4 ml-1"})]})]})},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6735:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(687),e=c(474),f=c(5814),g=c.n(f);let h=({article:a})=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,d.jsx)(g(),{href:`/news/${a.id}`,className:"block",children:(0,d.jsx)("div",{className:"relative h-48 cursor-pointer",children:(0,d.jsx)(e.default,{src:"/placeholder-news.svg",alt:a.title,fill:!0,className:"object-cover transition-transform duration-300 hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})})}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2",children:[(0,d.jsx)("span",{children:new Date(a.date_created).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}),a.select_category&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"mx-2",children:"•"}),(0,d.jsxs)("span",{children:["分类 ",a.select_category]})]})]}),(0,d.jsx)(g(),{href:`/news/${a.id}`,children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors duration-200 cursor-pointer",children:a.title})}),a.content&&(0,d.jsxs)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-3",children:[a.content.replace(/<[^>]*>/g,"").substring(0,100),"..."]}),(0,d.jsx)(g(),{href:`/news/${a.id}`,className:"inline-block text-blue-600 hover:text-blue-800 font-medium",children:"阅读更多 →"})]})]})},6976:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\个人文档\\\\dev\\\\starqiye-main\\\\src\\\\app\\\\news\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\app\\news\\page.tsx","default")},8354:a=>{"use strict";a.exports=require("util")},8358:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["news",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,6976)),"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\app\\news\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,5697)),"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\app\\news\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/news/page",pathname:"/news",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/news/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},8899:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(687),e=c(3210),f=c(2688);let g=(0,f.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),h=(0,f.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),i=({categories:a,selectedCategory:b,onCategorySelect:c,title:f,allItemsLabel:i})=>{let[j,k]=(0,e.useState)(!1),l=b?a.find(a=>a.id===b)?.name:i;return(0,d.jsx)("div",{className:"lg:hidden mb-6",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,d.jsxs)("button",{onClick:()=>k(!j),className:"w-full px-4 py-3 flex items-center justify-between text-left",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm text-gray-500",children:f}),(0,d.jsx)("div",{className:"font-medium text-gray-900",children:l})]}),j?(0,d.jsx)(g,{className:"h-5 w-5 text-gray-400"}):(0,d.jsx)(h,{className:"h-5 w-5 text-gray-400"})]}),j&&(0,d.jsx)("div",{className:"border-t border-gray-200 p-4",children:(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>{c(null),k(!1)},className:`w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ${null===b?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:i})}),a.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)("button",{onClick:()=>{c(a.id),k(!1)},className:`w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ${b===a.id?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:a.name})},a.id))]})})]})})}},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,726,474,864],()=>b(b.s=8358));module.exports=c})();