(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{3152:(e,t,a)=>{"use strict";a.d(t,{vO:()=>o,GE:()=>c,bd:()=>i,bW:()=>u,zj:()=>g,WE:()=>_,oo:()=>p,ql:()=>m,d$:()=>d,Ct:()=>h});var r=a(8453);let n={siteId:"default",siteName:"Mendeleev企业网站",tablePrefix:"company_mendeleev_",tables:{products:"company_mendeleev_products",productCategories:"company_mendeleev_product_categories",article:"company_mendeleev_article",categories:"company_mendeleev_categories",pages:"company_mendeleev_pages",config:"company_mendeleev_config"}},s=n.tables,l=(0,r.ieq)("http://*************:8055").with((0,r.zs8)()),c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let n={status:{_eq:"published"}};return e&&(n.select_category={_eq:e}),await l.request((0,r.F1f)(s.article,{filter:n,limit:t,offset:(a-1)*t,sort:["-date_created"],fields:["*"]}))}catch(e){return[]}},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let n={status:{_eq:"published"}};e&&(n.select_category={_eq:e});let c=await l.request((0,r.F1f)(s.article,{filter:n,limit:t+1,offset:(a-1)*t,sort:["-date_created"],fields:["*"]})),i=c.length>t,o=i?c.slice(0,t):c,u=a;i&&(u=a+1);let d=i?a*t+1:(a-1)*t+o.length;return{data:o,meta:{total_count:d,filter_count:d,has_next_page:i,current_page:a,total_pages:u}}}catch(e){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:a,total_pages:1}}}},o=async e=>{try{return await l.request((0,r.k0O)(s.article,parseInt(e),{fields:["*"]}))}catch(e){return null}},u=async()=>{try{return await l.request((0,r.F1f)(s.categories,{sort:["name"]}))}catch(e){return[]}},d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let n={status:{_eq:"published"}};return e&&(n.category={_eq:e}),await l.request((0,r.F1f)(s.products,{filter:n,limit:t,offset:(a-1)*t,sort:["-created_at"],fields:["*"]}))}catch(e){return[]}},h=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let n={status:{_eq:"published"}};e&&(n.category={_eq:e});let c=await l.request((0,r.F1f)(s.products,{filter:n,limit:t+1,offset:(a-1)*t,sort:["-created_at"],fields:["*"]})),i=c.length>t,o=i?c.slice(0,t):c,u=a;i&&(u=a+1);let d=i?a*t+1:(a-1)*t+o.length;return{data:o,meta:{total_count:d,filter_count:d,has_next_page:i,current_page:a,total_pages:u}}}catch(e){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:a,total_pages:1}}}},p=async e=>{try{return await l.request((0,r.k0O)(s.products,parseInt(e),{fields:["*","category.name","category.id"]}))}catch(e){return null}},m=async()=>{try{return await l.request((0,r.F1f)(s.productCategories,{sort:["name"]}))}catch(e){return[{id:1,name:"拍卖品",description:"拍卖相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:2,name:"艺术品",description:"艺术品相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]}},_=async e=>{try{let t=(await l.request((0,r.F1f)(s.pages,{filter:{slug:{_eq:e},status:{_eq:"published"}},limit:1})))[0];if(!t)return null;return t}catch(e){return null}},g=async()=>{try{let e=await fetch("".concat("http://*************:8055","/items/").concat(s.config,"?fields=*&filter=%7B%22status%22%3A%7B%22_eq%22%3A%22published%22%7D%7D&limit=1"));if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(t&&t.data)return t.data;return null}catch(e){return null}}},4802:(e,t,a)=>{Promise.resolve().then(a.bind(a,5550))},4951:(e,t,a)=>{"use strict";a.d(t,{ConfigProvider:()=>i,U:()=>c});var r=a(5155),n=a(2115),s=a(3152);let l=(0,n.createContext)({config:null,loading:!0}),c=()=>(0,n.useContext)(l),i=e=>{let{children:t}=e,[a,c]=(0,n.useState)(null),[i,o]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{(async()=>{try{let e=await (0,s.zj)(),t=null;Array.isArray(e)&&e.length>0?t=e[0]:e&&!Array.isArray(e)&&(t=e),t&&t.title&&c(t)}catch(e){}finally{o(!1)}})()},[]),(0,r.jsx)(l.Provider,{value:{config:a,loading:i},children:t})}},5550:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(5155),n=a(2115),s=a(3152),l=a(4951);function c(){let[e,t]=(0,n.useState)(null),[a,c]=(0,n.useState)(!0),{config:i}=(0,l.U)();return((0,n.useEffect)(()=>{(async()=>{try{let e=await (0,s.WE)("about");t(e)}catch(e){}finally{c(!1)}})()},[]),(0,n.useEffect)(()=>{if(i){document.title="关于我们-".concat(i.title);let e=document.querySelector('meta[name="description"]');if(e)e.setAttribute("content",i.description||"了解我们的公司信息和发展历程");else{let e=document.createElement("meta");e.name="description",e.content=i.description||"了解我们的公司信息和发展历程",document.head.appendChild(e)}}},[i]),a)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:(null==e?void 0:e.title)||"关于我们"})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-8",children:(null==e?void 0:e.content)?(0,r.jsx)("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:e.content}}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500 text-lg",children:"内容正在配置中，请稍后查看"})})})})]})}}},e=>{e.O(0,[453,441,964,358],()=>e(e.s=4802)),_N_E=e.O()}]);