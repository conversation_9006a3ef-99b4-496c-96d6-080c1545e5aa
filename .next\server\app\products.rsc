1:"$Sreact.fragment"
2:I[4951,["453","static/chunks/453-e82c603efa2f06be.js","874","static/chunks/874-437a265a67d6cfee.js","177","static/chunks/app/layout-fcbd72a765c25e54.js"],"ConfigProvider"]
3:I[5247,["453","static/chunks/453-e82c603efa2f06be.js","874","static/chunks/874-437a265a67d6cfee.js","177","static/chunks/app/layout-fcbd72a765c25e54.js"],"default"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[6821,["453","static/chunks/453-e82c603efa2f06be.js","874","static/chunks/874-437a265a67d6cfee.js","177","static/chunks/app/layout-fcbd72a765c25e54.js"],"default"]
7:I[894,[],"ClientPageRoot"]
8:I[1057,["453","static/chunks/453-e82c603efa2f06be.js","874","static/chunks/874-437a265a67d6cfee.js","766","static/chunks/766-41587ae232ab9347.js","407","static/chunks/407-b9a9f618274719dd.js","571","static/chunks/app/products/page-db3c4525d07319a2.js"],"default"]
b:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[9665,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[8393,[],""]
:HL["/_next/static/css/b5da0a0488a7710a.css","style"]
0:{"P":null,"b":"i-wbVUOJEegAgh5Yn-dnv","p":"","c":["","products"],"i":false,"f":[[["",{"children":["products",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b5da0a0488a7710a.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"zh-CN","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-grow","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}]]}]}]}]}]]}],{"children":["products",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
9:{}
a:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:I[8175,[],"IconMark"]
e:{"metadata":[["$","link","0",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L15","1",{}]],"error":null,"digest":"$undefined"}
13:"$e:metadata"
