(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{246:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),r=a(5695),n=a(6766),l=a(6874),c=a.n(l),i=a(2115),d=a(3152),o=a(4951);function u(e){let{params:t}=e,[a,l]=(0,i.useState)(null),[u,m]=(0,i.useState)(!0),[h,p]=(0,i.useState)(""),{config:x}=(0,o.U)();if((0,i.useEffect)(()=>{(async()=>{p((await t).id)})()},[t]),(0,i.useEffect)(()=>{h&&(async()=>{try{let e=await (0,d.oo)(h);e||(0,r.notFound)(),l(e)}catch(e){(0,r.notFound)()}finally{m(!1)}})()},[h]),(0,i.useEffect)(()=>{if(a&&x){document.title="".concat(a.name,"-").concat(x.title);let e=document.querySelector('meta[name="description"]'),t=a.short_description||x.description||"".concat(a.name," - 产品详情");if(e)e.setAttribute("content",t);else{let e=document.createElement("meta");e.name="description",e.content=t,document.head.appendChild(e)}}},[a,x]),u)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})});a||(0,r.notFound)();let g=a.main_image?"".concat("http://*************:8055","/assets/").concat(a.main_image,"?updated=").concat(a.updated_at||a.created_at):"/placeholder-product.svg",f=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"});return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white shadow-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("nav",{className:"flex","aria-label":"Breadcrumb",children:(0,s.jsxs)("ol",{className:"flex items-center space-x-4",children:[(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/",className:"text-gray-500 hover:text-gray-700",children:"首页"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-gray-400",children:"/"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/products",className:"text-gray-500 hover:text-gray-700",children:"产品展示"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-gray-400",children:"/"})}),(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-gray-900 font-medium",children:a.name})})]})})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8",children:[(0,s.jsx)("div",{className:"relative h-96 lg:h-full min-h-[400px]",children:(0,s.jsx)(n.default,{src:g,alt:a.name,fill:!0,className:"object-cover rounded-lg",sizes:"(max-width: 1024px) 100vw, 50vw"})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:a.name}),a.category&&(0,s.jsxs)("p",{className:"text-blue-600 font-medium",children:["分类：","object"==typeof a.category?a.category.name:"分类 ".concat(a.category)]})]}),a.price&&(0,s.jsxs)("div",{className:"text-3xl font-bold text-blue-600",children:["\xa5",parseFloat(a.price).toLocaleString()]}),a.short_description&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"产品简介"}),(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed",children:a.short_description})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,s.jsxs)("p",{children:["发布时间: ",f(a.created_at)]}),a.updated_at!==a.created_at&&(0,s.jsxs)("p",{children:["更新时间: ",f(a.updated_at)]})]})]})]}),a.description&&(0,s.jsxs)("div",{className:"border-t border-gray-200 p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"产品详情"}),(0,s.jsx)("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:a.description}})]})]}),(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)(c(),{href:"/products",className:"inline-flex items-center text-blue-600 hover:text-blue-800 font-medium",children:"← 返回产品列表"})})]})]})}},3152:(e,t,a)=>{"use strict";a.d(t,{vO:()=>d,GE:()=>c,bd:()=>i,bW:()=>o,zj:()=>g,WE:()=>x,oo:()=>h,ql:()=>p,d$:()=>u,Ct:()=>m});var s=a(8453);let r={siteId:"default",siteName:"Mendeleev企业网站",tablePrefix:"company_mendeleev_",tables:{products:"company_mendeleev_products",productCategories:"company_mendeleev_product_categories",article:"company_mendeleev_article",categories:"company_mendeleev_categories",pages:"company_mendeleev_pages",config:"company_mendeleev_config"}},n=r.tables,l=(0,s.ieq)("http://*************:8055").with((0,s.zs8)()),c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let r={status:{_eq:"published"}};return e&&(r.select_category={_eq:e}),await l.request((0,s.F1f)(n.article,{filter:r,limit:t,offset:(a-1)*t,sort:["-date_created"],fields:["*"]}))}catch(e){return[]}},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let r={status:{_eq:"published"}};e&&(r.select_category={_eq:e});let c=await l.request((0,s.F1f)(n.article,{filter:r,limit:t+1,offset:(a-1)*t,sort:["-date_created"],fields:["*"]})),i=c.length>t,d=i?c.slice(0,t):c,o=a;i&&(o=a+1);let u=i?a*t+1:(a-1)*t+d.length;return{data:d,meta:{total_count:u,filter_count:u,has_next_page:i,current_page:a,total_pages:o}}}catch(e){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:a,total_pages:1}}}},d=async e=>{try{return await l.request((0,s.k0O)(n.article,parseInt(e),{fields:["*"]}))}catch(e){return null}},o=async()=>{try{return await l.request((0,s.F1f)(n.categories,{sort:["name"]}))}catch(e){return[]}},u=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let r={status:{_eq:"published"}};return e&&(r.category={_eq:e}),await l.request((0,s.F1f)(n.products,{filter:r,limit:t,offset:(a-1)*t,sort:["-created_at"],fields:["*"]}))}catch(e){return[]}},m=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;try{let r={status:{_eq:"published"}};e&&(r.category={_eq:e});let c=await l.request((0,s.F1f)(n.products,{filter:r,limit:t+1,offset:(a-1)*t,sort:["-created_at"],fields:["*"]})),i=c.length>t,d=i?c.slice(0,t):c,o=a;i&&(o=a+1);let u=i?a*t+1:(a-1)*t+d.length;return{data:d,meta:{total_count:u,filter_count:u,has_next_page:i,current_page:a,total_pages:o}}}catch(e){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:a,total_pages:1}}}},h=async e=>{try{return await l.request((0,s.k0O)(n.products,parseInt(e),{fields:["*","category.name","category.id"]}))}catch(e){return null}},p=async()=>{try{return await l.request((0,s.F1f)(n.productCategories,{sort:["name"]}))}catch(e){return[{id:1,name:"拍卖品",description:"拍卖相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:2,name:"艺术品",description:"艺术品相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]}},x=async e=>{try{let t=(await l.request((0,s.F1f)(n.pages,{filter:{slug:{_eq:e},status:{_eq:"published"}},limit:1})))[0];if(!t)return null;return t}catch(e){return null}},g=async()=>{try{let e=await fetch("".concat("http://*************:8055","/items/").concat(n.config,"?fields=*&filter=%7B%22status%22%3A%7B%22_eq%22%3A%22published%22%7D%7D&limit=1"));if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(t&&t.data)return t.data;return null}catch(e){return null}}},4951:(e,t,a)=>{"use strict";a.d(t,{ConfigProvider:()=>i,U:()=>c});var s=a(5155),r=a(2115),n=a(3152);let l=(0,r.createContext)({config:null,loading:!0}),c=()=>(0,r.useContext)(l),i=e=>{let{children:t}=e,[a,c]=(0,r.useState)(null),[i,d]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{try{let e=await (0,n.zj)(),t=null;Array.isArray(e)&&e.length>0?t=e[0]:e&&!Array.isArray(e)&&(t=e),t&&t.title&&c(t)}catch(e){}finally{d(!1)}})()},[]),(0,s.jsx)(l.Provider,{value:{config:a,loading:i},children:t})}},4976:(e,t,a)=>{Promise.resolve().then(a.bind(a,246))},5695:(e,t,a)=>{"use strict";var s=a(8999);a.o(s,"notFound")&&a.d(t,{notFound:function(){return s.notFound}})}},e=>{e.O(0,[453,874,766,441,964,358],()=>e(e.s=4976)),_N_E=e.O()}]);