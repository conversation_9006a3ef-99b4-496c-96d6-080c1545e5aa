exports.id=864,exports.ids=[864],exports.modules={440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},1135:()=>{},1317:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(687),e=c(5814),f=c.n(e),g=c(5093);let h=()=>{let{config:a}=(0,g.U)();return(0,d.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-blue-400 mb-4",children:a?.title||"企业网站"}),(0,d.jsx)("p",{className:"text-gray-300 mb-4",children:a?.description||"我们致力于为客户提供优质的产品和服务，以创新为驱动，以质量为根本，努力成为行业领先的企业。"}),(0,d.jsxs)("div",{className:"text-gray-300",children:[a?.address&&(0,d.jsxs)("p",{className:"mb-2",children:["地址：",a.address]}),a?.phone&&(0,d.jsxs)("p",{className:"mb-2",children:["电话：",a.phone]}),a?.email&&(0,d.jsxs)("p",{className:"mb-2",children:["邮箱：",a.email]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"快速链接"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"首页"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/products",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"产品展示"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/news",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"新闻中心"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/about",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"关于我们"})}),(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:"/contact",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"联系我们"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"服务支持"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"技术支持"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"售后服务"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors duration-200",children:"常见问题"})})]})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center",children:(0,d.jsxs)("p",{className:"text-gray-400",children:["\xa9 2025 ",a?.title||"企业网站",". 保留所有权利.",a?.icp_beian&&(0,d.jsxs)(d.Fragment,{children:[" | ",(0,d.jsx)("a",{href:"https://beian.miit.gov.cn",className:"hover:text-blue-400 transition-colors duration-200 ml-1",children:a.icp_beian})]})]})})]})})}},1891:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(687),e=c(3210),f=c(5814),g=c.n(f),h=c(1860),i=c(2941),j=c(5093);let k=()=>{let[a,b]=(0,e.useState)(!1),{config:c,loading:f}=(0,j.U)(),k=[{name:"首页",href:"/"},{name:"产品展示",href:"/products"},{name:"新闻中心",href:"/news"},{name:"关于我们",href:"/about"},{name:"联系我们",href:"/contact"}];return(0,d.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(g(),{href:"/",className:"text-2xl font-bold text-blue-600",children:c?c.title:"企业网站"})}),(0,d.jsx)("nav",{className:"hidden md:flex space-x-8",children:k.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:a.name},a.name))}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)("button",{onClick:()=>b(!a),className:"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600",children:a?(0,d.jsx)(h.A,{className:"h-6 w-6"}):(0,d.jsx)(i.A,{className:"h-6 w-6"})})})]}),a&&(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:k.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200",onClick:()=>b(!1),children:a.name},a.name))})})]})})}},2178:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},3722:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},4597:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\个人文档\\\\dev\\\\starqiye-main\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\components\\Header.tsx","default")},4726:(a,b,c)=>{Promise.resolve().then(c.bind(c,8659)),Promise.resolve().then(c.bind(c,4597)),Promise.resolve().then(c.bind(c,7087))},5093:(a,b,c)=>{"use strict";c.d(b,{ConfigProvider:()=>i,U:()=>h});var d=c(687),e=c(3210),f=c(9128);let g=(0,e.createContext)({config:null,loading:!0}),h=()=>(0,e.useContext)(g),i=({children:a})=>{let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(!0);return(0,e.useEffect)(()=>{(async()=>{try{let a=await (0,f.zj)(),b=null;Array.isArray(a)&&a.length>0?b=a[0]:a&&!Array.isArray(a)&&(b=a),b&&b.title&&c(b)}catch(a){}finally{i(!1)}})()},[]),(0,d.jsx)(g.Provider,{value:{config:b,loading:h},children:a})}},5697:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n,metadata:()=>m});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);var i=c(4597),j=c(8659),k=c(7087);let l=({children:a})=>(0,d.jsx)(k.ConfigProvider,{children:(0,d.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{className:"flex-grow",children:a}),(0,d.jsx)(j.default,{})]})}),m={title:"",description:""};function n({children:a}){return(0,d.jsx)("html",{lang:"zh-CN",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(l,{children:a})})})}},6350:(a,b,c)=>{Promise.resolve().then(c.bind(c,1317)),Promise.resolve().then(c.bind(c,1891)),Promise.resolve().then(c.bind(c,5093))},7087:(a,b,c)=>{"use strict";c.d(b,{ConfigProvider:()=>e});var d=c(1369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useConfig() from the server but useConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\contexts\\ConfigContext.tsx","useConfig");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\contexts\\ConfigContext.tsx","ConfigProvider")},8659:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\个人文档\\\\dev\\\\starqiye-main\\\\src\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\个人文档\\dev\\starqiye-main\\src\\components\\Footer.tsx","default")},9128:(a,b,c)=>{"use strict";c.d(b,{vO:()=>j,GE:()=>h,bd:()=>i,bW:()=>k,zj:()=>q,WE:()=>p,oo:()=>n,ql:()=>o,d$:()=>l,Ct:()=>m});var d=c(2271);let e={siteId:"default",siteName:"Mendeleev企业网站",tablePrefix:"company_mendeleev_",tables:{products:"company_mendeleev_products",productCategories:"company_mendeleev_product_categories",article:"company_mendeleev_article",categories:"company_mendeleev_categories",pages:"company_mendeleev_pages",config:"company_mendeleev_config"}},f=e.tables,g=(0,d.ieq)("http://*************:8055").with((0,d.zs8)()),h=async(a,b=20,c=1)=>{try{let e={status:{_eq:"published"}};return a&&(e.select_category={_eq:a}),await g.request((0,d.F1f)(f.article,{filter:e,limit:b,offset:(c-1)*b,sort:["-date_created"],fields:["*"]}))}catch(a){return[]}},i=async(a,b=20,c=1)=>{try{let e={status:{_eq:"published"}};a&&(e.select_category={_eq:a});let h=await g.request((0,d.F1f)(f.article,{filter:e,limit:b+1,offset:(c-1)*b,sort:["-date_created"],fields:["*"]})),i=h.length>b,j=i?h.slice(0,b):h,k=c;i&&(k=c+1);let l=i?c*b+1:(c-1)*b+j.length;return{data:j,meta:{total_count:l,filter_count:l,has_next_page:i,current_page:c,total_pages:k}}}catch(a){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:c,total_pages:1}}}},j=async a=>{try{return await g.request((0,d.k0O)(f.article,parseInt(a),{fields:["*"]}))}catch(a){return null}},k=async()=>{try{return await g.request((0,d.F1f)(f.categories,{sort:["name"]}))}catch(a){return[]}},l=async(a,b=20,c=1)=>{try{let e={status:{_eq:"published"}};return a&&(e.category={_eq:a}),await g.request((0,d.F1f)(f.products,{filter:e,limit:b,offset:(c-1)*b,sort:["-created_at"],fields:["*"]}))}catch(a){return[]}},m=async(a,b=20,c=1)=>{try{let e={status:{_eq:"published"}};a&&(e.category={_eq:a});let h=await g.request((0,d.F1f)(f.products,{filter:e,limit:b+1,offset:(c-1)*b,sort:["-created_at"],fields:["*"]})),i=h.length>b,j=i?h.slice(0,b):h,k=c;i&&(k=c+1);let l=i?c*b+1:(c-1)*b+j.length;return{data:j,meta:{total_count:l,filter_count:l,has_next_page:i,current_page:c,total_pages:k}}}catch(a){return{data:[],meta:{total_count:0,filter_count:0,has_next_page:!1,current_page:c,total_pages:1}}}},n=async a=>{try{return await g.request((0,d.k0O)(f.products,parseInt(a),{fields:["*","category.name","category.id"]}))}catch(a){return null}},o=async()=>{try{return await g.request((0,d.F1f)(f.productCategories,{sort:["name"]}))}catch(a){return[{id:1,name:"拍卖品",description:"拍卖相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:2,name:"艺术品",description:"艺术品相关产品",status:"published",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]}},p=async a=>{try{let b=(await g.request((0,d.F1f)(f.pages,{filter:{slug:{_eq:a},status:{_eq:"published"}},limit:1})))[0];if(!b)return null;return b}catch(a){return null}},q=async()=>{try{let a=await fetch(`http://*************:8055/items/${f.config}?fields=*&filter=%7B%22status%22%3A%7B%22_eq%22%3A%22published%22%7D%7D&limit=1`);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);let b=await a.json();if(b&&b.data)return b.data;return null}catch(a){return null}}}};