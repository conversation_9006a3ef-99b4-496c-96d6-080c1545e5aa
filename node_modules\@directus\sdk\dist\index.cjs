"use strict";var J=Object.defineProperty;var _=Object.getOwnPropertyDescriptor;var H=Object.getOwnPropertyNames;var V=Object.prototype.hasOwnProperty;var B=(e,t)=>{for(var o in t)J(e,o,{get:t[o],enumerable:!0})},M=(e,t,o,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of H(t))!V.call(e,s)&&s!==o&&J(e,s,{get:()=>t[s],enumerable:!(a=_(t,s))||a.enumerable});return e};var q=e=>M(J({},"__esModule",{value:!0}),e);var Aa={};B(Aa,{acceptUserInvite:()=>Sa,aggregate:()=>kt,auth:()=>N,authenticateShare:()=>pa,authentication:()=>Z,clearCache:()=>aa,compareContentVersion:()=>ga,createCollection:()=>ye,createComment:()=>Se,createComments:()=>de,createContentVersion:()=>qe,createContentVersions:()=>Me,createDashboard:()=>fe,createDashboards:()=>le,createDirectus:()=>te,createField:()=>Qe,createFlow:()=>ge,createFlows:()=>Ce,createFolder:()=>De,createFolders:()=>Re,createItem:()=>Oe,createItems:()=>be,createNotification:()=>je,createNotifications:()=>Pe,createOperation:()=>Ee,createOperations:()=>Ie,createPanel:()=>Ae,createPanels:()=>Fe,createPermission:()=>Ne,createPermissions:()=>we,createPolicies:()=>ve,createPolicy:()=>Ue,createPreset:()=>$e,createPresets:()=>Je,createRelation:()=>ke,createRole:()=>Ge,createRoles:()=>Le,createShare:()=>We,createShares:()=>Ke,createTranslation:()=>He,createTranslations:()=>_e,createUser:()=>Be,createUsers:()=>Ve,createWebhook:()=>Xe,createWebhooks:()=>ze,customEndpoint:()=>Ea,deleteCollection:()=>Ye,deleteComment:()=>et,deleteComments:()=>Ze,deleteContentVersion:()=>Nt,deleteContentVersions:()=>wt,deleteDashboard:()=>rt,deleteDashboards:()=>tt,deleteField:()=>ot,deleteFile:()=>st,deleteFiles:()=>at,deleteFlow:()=>it,deleteFlows:()=>mt,deleteFolder:()=>ct,deleteFolders:()=>nt,deleteItem:()=>ut,deleteItems:()=>pt,deleteNotification:()=>yt,deleteNotifications:()=>ht,deleteOperation:()=>St,deleteOperations:()=>dt,deletePanel:()=>ft,deletePanels:()=>lt,deletePermission:()=>xt,deletePermissions:()=>Qt,deletePolicies:()=>Tt,deletePolicy:()=>Ct,deletePreset:()=>Rt,deletePresets:()=>gt,deleteRelation:()=>Dt,deleteRole:()=>Ot,deleteRoles:()=>bt,deleteShare:()=>jt,deleteShares:()=>Pt,deleteTranslation:()=>Et,deleteTranslations:()=>It,deleteUser:()=>At,deleteUsers:()=>Ft,deleteWebhook:()=>Ut,deleteWebhooks:()=>vt,disableTwoFactor:()=>Ta,enableTwoFactor:()=>xa,formatFields:()=>U,generateHash:()=>ia,generateTwoFactorSecret:()=>Qa,generateUid:()=>v,getAuthEndpoint:()=>F,graphql:()=>oe,importFile:()=>Te,inviteShare:()=>ua,inviteUser:()=>da,isDirectusError:()=>Fa,login:()=>ie,logout:()=>ne,memoryStorage:()=>k,messageCallback:()=>A,passwordRequest:()=>ce,passwordReset:()=>pe,pong:()=>L,promoteContentVersion:()=>Ra,queryToParams:()=>$,randomString:()=>Da,readActivities:()=>Jt,readActivity:()=>$t,readAssetArrayBuffer:()=>Kt,readAssetBlob:()=>Gt,readAssetRaw:()=>Lt,readCollection:()=>_t,readCollections:()=>Wt,readComment:()=>Vt,readComments:()=>Ht,readContentVersion:()=>Lr,readContentVersions:()=>kr,readDashboard:()=>Mt,readDashboards:()=>Bt,readExtensions:()=>qt,readField:()=>Yt,readFields:()=>zt,readFieldsByCollection:()=>Xt,readFile:()=>er,readFiles:()=>Zt,readFlow:()=>rr,readFlows:()=>tr,readFolder:()=>ar,readFolders:()=>or,readGraphqlSdl:()=>Vr,readItem:()=>mr,readItemPermissions:()=>Sr,readItems:()=>sr,readMe:()=>$r,readNotification:()=>nr,readNotifications:()=>ir,readOpenApiSpec:()=>qr,readOperation:()=>pr,readOperations:()=>cr,readPanel:()=>hr,readPanels:()=>ur,readPermission:()=>dr,readPermissions:()=>yr,readPolicies:()=>fr,readPolicy:()=>Qr,readPolicyGlobals:()=>xr,readPreset:()=>Cr,readPresets:()=>Tr,readProviders:()=>ue,readRelation:()=>Dr,readRelationByCollection:()=>Rr,readRelations:()=>gr,readRevision:()=>Or,readRevisions:()=>br,readRole:()=>jr,readRoles:()=>Pr,readRolesMe:()=>Ir,readSettings:()=>Er,readShare:()=>Ar,readShareInfo:()=>ha,readShares:()=>Fr,readSingleton:()=>wr,readTranslation:()=>vr,readTranslations:()=>Nr,readUser:()=>Jr,readUserPermissions:()=>lr,readUsers:()=>Ur,readWebhook:()=>Kr,readWebhooks:()=>Gr,realtime:()=>se,refresh:()=>he,registerUser:()=>la,registerUserVerify:()=>fa,rest:()=>Oa,saveToContentVersion:()=>Ca,schemaApply:()=>Wr,schemaDiff:()=>_r,schemaSnapshot:()=>Hr,serverHealth:()=>Br,serverInfo:()=>Mr,serverPing:()=>zr,sleep:()=>me,staticToken:()=>ee,throwIfCoreCollection:()=>f,throwIfEmpty:()=>r,triggerFlow:()=>ma,updateCollection:()=>Xr,updateCollectionsBatch:()=>Yr,updateComment:()=>eo,updateComments:()=>Zr,updateContentVersion:()=>ta,updateContentVersions:()=>Zo,updateContentVersionsBatch:()=>ea,updateDashboard:()=>oo,updateDashboards:()=>to,updateDashboardsBatch:()=>ro,updateExtension:()=>ao,updateField:()=>so,updateFile:()=>no,updateFiles:()=>mo,updateFilesBatch:()=>io,updateFlow:()=>uo,updateFlows:()=>co,updateFlowsBatch:()=>po,updateFolder:()=>So,updateFolders:()=>ho,updateFoldersBatch:()=>yo,updateItem:()=>Qo,updateItems:()=>lo,updateItemsBatch:()=>fo,updateMe:()=>Yo,updateNotification:()=>Co,updateNotifications:()=>xo,updateNotificationsBatch:()=>To,updateOperation:()=>Do,updateOperations:()=>go,updateOperationsBatch:()=>Ro,updatePanel:()=>Po,updatePanels:()=>bo,updatePanelsBatch:()=>Oo,updatePermission:()=>Eo,updatePermissions:()=>jo,updatePermissionsBatch:()=>Io,updatePolicies:()=>Fo,updatePoliciesBatch:()=>Ao,updatePolicy:()=>wo,updatePreset:()=>Uo,updatePresets:()=>No,updatePresetsBatch:()=>vo,updateRelation:()=>Jo,updateRole:()=>Lo,updateRoles:()=>$o,updateRolesBatch:()=>ko,updateSettings:()=>Go,updateShare:()=>_o,updateShares:()=>Ko,updateSharesBatch:()=>Wo,updateSingleton:()=>Ho,updateTranslation:()=>Mo,updateTranslations:()=>Vo,updateTranslationsBatch:()=>Bo,updateUser:()=>Xo,updateUsers:()=>qo,updateUsersBatch:()=>zo,updateWebhook:()=>oa,updateWebhooks:()=>ra,uploadFiles:()=>xe,utilitySort:()=>ya,utilsExport:()=>sa,utilsImport:()=>ca,verifyHash:()=>na,withOptions:()=>Pa,withSearch:()=>ja,withToken:()=>Ia});module.exports=q(Aa);function F(e){return e?`/auth/login/${e}`:"/auth/login"}var w="/",z=(e,t)=>(e.endsWith(w)&&(e=e.slice(0,-1)),t.startsWith(w)||(t=w+t),e+t),R=(e,t,o)=>{let a=e.pathname===w?t:z(e.pathname,t),s=new globalThis.URL(a,e);if(o)for(let[c,n]of Object.entries($(o)))if(n&&typeof n=="object"&&!Array.isArray(n))for(let[p,u]of Object.entries(n))s.searchParams.set(`${c}[${p}]`,String(u));else s.searchParams.set(c,n);return s};function G(e){return typeof e!="object"||!e?!1:"headers"in e&&"ok"in e&&"json"in e&&typeof e.json=="function"&&"text"in e&&typeof e.json=="function"}async function K(e){if(!(typeof e!="object"||!e)){if(G(e)){let t=e.headers.get("Content-Type")?.toLowerCase();if(t?.startsWith("application/json")||t?.startsWith("application/health+json")){let o=await e.json();if(!e.ok||"errors"in o)throw o;return"data"in o?o.data:o}if(t?.startsWith("text/html")||t?.startsWith("text/plain")){let o=await e.text();if(!e.ok)throw o;return o}return e.status===204?null:e}if("errors"in e)throw e;return"data"in e?e.data:e}}var D=async(e,t,o=globalThis.fetch)=>(t.headers=typeof t.headers=="object"&&!Array.isArray(t.headers)?t.headers:{},o(e,t).then(a=>K(a).catch(s=>{let c={errors:s&&typeof s=="object"&&"errors"in s?s.errors:s,response:a};return s&&typeof s=="object"&&"data"in s&&(c.data=s.data),Promise.reject(c)})));var k=()=>{let e=null;return{get:async()=>e,set:async t=>{e=t}}};var X={msRefreshBeforeExpires:3e4,autoRefresh:!0},Y=2**31-1,Z=(e="cookie",t={})=>o=>{let a={...X,...t},s=null,c=null,n=a.storage??k(),p=async()=>n.set({access_token:null,refresh_token:null,expires:null,expires_at:null}),u=async()=>{try{await s}finally{s=null}},Q=async()=>{let d=await n.get();return s||!d?.expires_at||d.expires_at<new Date().getTime()+a.msRefreshBeforeExpires&&I().catch(l=>{}),u()},b=async d=>{let l=d.expires??0;d.expires_at=new Date().getTime()+l,await n.set(d),a.autoRefresh&&l>a.msRefreshBeforeExpires&&l<Y&&(c&&clearTimeout(c),c=setTimeout(()=>{c=null,I().catch(x=>{})},l-a.msRefreshBeforeExpires))},I=async(d={})=>(s=(async()=>{let x=await n.get();await p();let m={method:"POST",headers:{"Content-Type":"application/json"}};"credentials"in a&&(m.credentials=a.credentials);let i={mode:d.mode??e};e==="json"&&x?.refresh_token&&(i.refresh_token=x.refresh_token),m.body=JSON.stringify(i);let h=R(o.url,"/auth/refresh");return D(h.toString(),m,o.globals.fetch).then(y=>b(y).then(()=>y))})(),s);async function C(d,l={}){await p();let x=d;"otp"in l&&(x.otp=l.otp),x.mode=l.mode??e;let m=F(l.provider),i=R(o.url,m),h={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(x)};"credentials"in a&&(h.credentials=a.credentials);let y=await D(i.toString(),h,o.globals.fetch);return await b(y),y}return{refresh:I,login:C,async logout(d={}){let l=await n.get(),x={method:"POST",headers:{"Content-Type":"application/json"}};"credentials"in a&&(x.credentials=a.credentials);let m={mode:d.mode??e};e==="json"&&l?.refresh_token&&(m.refresh_token=l.refresh_token),x.body=JSON.stringify(m);let i=R(o.url,"/auth/logout");await D(i.toString(),x,o.globals.fetch),this.stopRefreshing(),await p()},stopRefreshing(){c&&clearTimeout(c)},async getToken(){return await Q().catch(()=>{}),(await n.get())?.access_token??null},async setToken(d){return n.set({access_token:d,refresh_token:null,expires:null,expires_at:null})}}};var ee=e=>t=>{let o=e??null;return{async getToken(){return o},async setToken(a){o=a}}};var W={fetch:globalThis.fetch,WebSocket:globalThis.WebSocket,URL:globalThis.URL,logger:globalThis.console},te=(e,t={})=>{let o=t.globals?{...W,...t.globals}:W;return{globals:o,url:new o.URL(e),with(a){return{...this,...a(this)}}}};var re={},oe=(e={})=>t=>{let o={...re,...e};return{async query(a,s,c="items"){let n={method:"POST",body:JSON.stringify({query:a,variables:s})};"credentials"in o&&(n.credentials=o.credentials);let p={};if("getToken"in this){let b=await this.getToken();b&&(p.Authorization=`Bearer ${b}`)}"Content-Type"in p||(p["Content-Type"]="application/json"),n.headers=p;let u=c==="items"?"/graphql":"/graphql/system",Q=R(t.url,u);return await D(Q.toString(),n,t.globals.fetch)}}};function N(e){return JSON.stringify({...e,type:"auth"})}var L=()=>JSON.stringify({type:"pong"});function*v(){let e=1;for(;;)yield String(e),e++}var A=(e,t=1e3)=>new Promise((o,a)=>{let s=u=>{try{let Q=JSON.parse(u.data);typeof Q=="object"&&!Array.isArray(Q)&&Q!==null?(n(),o(Q)):(n(),c())}catch{n(),o(u)}},c=()=>a(),n=()=>{clearTimeout(p),e.removeEventListener("message",s),e.removeEventListener("error",c),e.removeEventListener("close",c)};e.addEventListener("message",s),e.addEventListener("error",c),e.addEventListener("close",c);let p=setTimeout(()=>{n(),o(void 0)},t)});var ae={authMode:"handshake",heartbeat:!0,debug:!1,reconnect:{delay:1e3,retries:10}};function se(e={}){return t=>{e={...ae,...e};let o=v(),a={code:"closed"},s={attempts:0,active:!1},c=!1,n=new Set,p=m=>"getToken"in m,u=(m,...i)=>e.debug&&t.globals.logger[m]("[Directus SDK]",...i),Q=async(m,i)=>{let h=new t.globals.URL(m);if(e.authMode==="strict"&&p(i)){let y=await i.getToken();y&&h.searchParams.set("access_token",y)}return h.toString()},b=async m=>{if("url"in e)return await Q(e.url,m);if(["ws:","wss:"].includes(t.url.protocol))return await Q(t.url,m);let i=new t.globals.URL(t.url.toString());return i.protocol=t.url.protocol==="https:"?"wss:":"ws:",i.pathname="/websocket",await Q(i,m)},I=m=>{let i=new Promise((h,y)=>{if(!e.reconnect||c)return y();if(u("info",`reconnect #${s.attempts} `+(s.attempts>=e.reconnect.retries?"maximum retries reached":`trying again in ${Math.max(100,e.reconnect.delay)}ms`)),s.active)return s.active;if(s.attempts>=e.reconnect.retries)return s.attempts=-1,y();setTimeout(()=>m.connect().then(O=>(n.forEach(S=>{m.sendMessage(S)}),O)).then(h).catch(y),Math.max(100,e.reconnect.delay))});s.attempts+=1,s.active=i.catch(()=>{}).finally(()=>{s.active=!1})},C={open:new Set([]),error:new Set([]),close:new Set([]),message:new Set([])};function d(m){return"type"in m&&"status"in m&&"error"in m&&"code"in m.error&&"message"in m.error&&m.type==="auth"&&m.status==="error"}async function l(m,i){if(a.code==="open"){if(m.error.code==="TOKEN_EXPIRED"&&(u("warn","Authentication token expired!"),p(i))){let h=await i.getToken();if(!h)throw Error("No token for re-authenticating the websocket");a.connection.send(N({access_token:h}))}if(m.error.code==="AUTH_TIMEOUT")return a.firstMessage&&e.authMode==="public"?(u("warn",'Authentication failed! Currently the "authMode" is "public" try using "handshake" instead'),e.reconnect=!1):u("warn","Authentication timed out!"),a.connection.close();if(m.error.code==="AUTH_FAILED"){if(a.firstMessage&&e.authMode==="public")return u("warn",'Authentication failed! Currently the "authMode" is "public" try using "handshake" instead'),e.reconnect=!1,a.connection.close();u("warn","Authentication failed!")}}}let x=async m=>{for(;a.code==="open";){let i=await A(a.connection).catch(()=>{});if(i){if(d(i)){await l(i,m),a.firstMessage=!1;continue}if(e.heartbeat&&i.type==="ping"){a.connection.send(L()),a.firstMessage=!1;continue}C.message.forEach(h=>{a.code==="open"&&h.call(a.connection,i)}),a.firstMessage=!1}}};return{async connect(){if(c=!1,a.code==="connecting")return await a.connection;if(a.code!=="closed")throw new Error(`Cannot connect when state is "${a.code}"`);let m=this,i=await b(m);u("info",`Connecting to ${i}...`);let h=new Promise((y,O)=>{let S=!1,T=new t.globals.WebSocket(i);T.addEventListener("open",async P=>{if(u("info","Connection open."),a={code:"open",connection:T,firstMessage:!0},s.attempts=0,s.active=!1,x(m),e.authMode==="handshake"&&p(m)){let g=await m.getToken();if(!g)throw Error("No token for authenticating the websocket. Make sure to provide one or call the login() function beforehand.");T.send(N({access_token:g}));let E=await A(T);if(E&&"type"in E&&"status"in E&&E.type==="auth"&&E.status==="ok")u("info","Authentication successful!");else return O("Authentication failed while opening websocket connection")}C.open.forEach(g=>g.call(T,P)),S=!0,y(T)}),T.addEventListener("error",P=>{u("warn","Connection errored."),C.error.forEach(g=>g.call(T,P)),T.close(),a={code:"error"},S||O(P)}),T.addEventListener("close",P=>{u("info","Connection closed."),C.close.forEach(g=>g.call(T,P)),o=v(),a={code:"closed"},I(this),S||O(P)})});return a={code:"connecting",connection:h},h},disconnect(){c=!0,a.code==="open"&&a.connection.close()},onWebSocket(m,i){if(m==="message"){let h=function(y){if(typeof y.data!="string")return i.call(this,y);try{return i.call(this,JSON.parse(y.data))}catch{return i.call(this,y)}};return C[m].add(h),()=>C[m].delete(h)}return C[m].add(i),()=>C[m].delete(i)},sendMessage(m){if(a.code!=="open")throw new Error('Cannot send messages without an open connection. Make sure you are calling "await client.connect()".');if(typeof m=="string")return a.connection.send(m);"uid"in m||(m.uid=o.next().value),a.connection.send(JSON.stringify(m))},async subscribe(m,i={}){"uid"in i||(i.uid=o.next().value),n.add({...i,collection:m,type:"subscribe"}),a.code!=="open"&&(u("info","No connection available for subscribing!"),await this.connect()),this.sendMessage({...i,collection:m,type:"subscribe"});let h=!0;async function*y(){for(;h&&a.code==="open";){let S=await A(a.connection).catch(()=>{});if(S){if("type"in S&&"status"in S&&S.type==="subscribe"&&S.status==="error")throw S;"type"in S&&"uid"in S&&S.type==="subscription"&&S.uid===i.uid&&(yield S)}}e.reconnect&&s.active&&(await s.active,a.code==="open"&&(a.connection.send(JSON.stringify({...i,collection:m,type:"subscribe"})),yield*y()))}let O=()=>{n.delete({...i,collection:m,type:"subscribe"}),this.sendMessage({uid:i.uid,type:"unsubscribe"}),h=!1};return{subscription:y(),unsubscribe:O}}}}}var me=e=>new Promise(t=>setTimeout(()=>t(),e));function ie(e,t={}){return()=>{let o=F(t.provider),a=e;return"otp"in t&&(a.otp=t.otp),a.mode=t.mode??"cookie",{path:o,method:"POST",body:JSON.stringify(a)}}}var ne=(e={})=>()=>{let t={mode:e.mode??"cookie"};return t.mode==="json"&&e.refresh_token&&(t.refresh_token=e.refresh_token),{path:"/auth/logout",method:"POST",body:JSON.stringify(t)}};var ce=(e,t)=>()=>({path:"/auth/password/request",method:"POST",body:JSON.stringify({email:e,...t?{reset_url:t}:{}})});var pe=(e,t)=>()=>({path:"/auth/password/reset",method:"POST",body:JSON.stringify({token:e,password:t})});var ue=(e=!1)=>()=>({path:e?"/auth?sessionOnly":"/auth",method:"GET"});var he=(e={})=>()=>{let t={mode:e.mode??"cookie"};return t.mode==="json"&&e.refresh_token&&(t.refresh_token=e.refresh_token),{path:"/auth/refresh",method:"POST",body:JSON.stringify(t)}};var ye=(e,t)=>()=>({path:"/collections",params:t??{},body:JSON.stringify(e),method:"POST"});var de=(e,t)=>()=>({path:"/comments",params:t??{},body:JSON.stringify(e),method:"POST"}),Se=(e,t)=>()=>({path:"/comments",params:t??{},body:JSON.stringify(e),method:"POST"});var le=(e,t)=>()=>({path:"/dashboards",params:t??{},body:JSON.stringify(e),method:"POST"}),fe=(e,t)=>()=>({path:"/dashboards",params:t??{},body:JSON.stringify(e),method:"POST"});var Qe=(e,t,o)=>()=>({path:`/fields/${e}`,params:o??{},body:JSON.stringify(t),method:"POST"});var xe=(e,t)=>()=>({path:"/files",method:"POST",body:e,params:t??{},headers:{"Content-Type":"multipart/form-data"}}),Te=(e,t={},o)=>()=>({path:"/files/import",method:"POST",body:JSON.stringify({url:e,data:t}),params:o??{}});var Ce=(e,t)=>()=>({path:"/flows",params:t??{},body:JSON.stringify(e),method:"POST"}),ge=(e,t)=>()=>({path:"/flows",params:t??{},body:JSON.stringify(e),method:"POST"});var Re=(e,t)=>()=>({path:"/folders",params:t??{},body:JSON.stringify(e),method:"POST"}),De=(e,t)=>()=>({path:"/folders",params:t??{},body:JSON.stringify(e),method:"POST"});function j(e){return["directus_access","directus_activity","directus_collections","directus_comments","directus_fields","directus_files","directus_folders","directus_migrations","directus_permissions","directus_policies","directus_presets","directus_relations","directus_revisions","directus_roles","directus_sessions","directus_settings","directus_users","directus_webhooks","directus_dashboards","directus_panels","directus_notifications","directus_shares","directus_flows","directus_operations","directus_translations","directus_versions","directus_extensions"].includes(e)}var be=(e,t,o)=>()=>{let a=String(e);if(j(a))throw new Error("Cannot use createItems for core collections");return{path:`/items/${a}`,params:o??{},body:JSON.stringify(t),method:"POST"}},Oe=(e,t,o)=>()=>{let a=String(e);if(j(a))throw new Error("Cannot use createItem for core collections");return{path:`/items/${a}`,params:o??{},body:JSON.stringify(t),method:"POST"}};var Pe=(e,t)=>()=>({path:"/notifications",params:t??{},body:JSON.stringify(e),method:"POST"}),je=(e,t)=>()=>({path:"/notifications",params:t??{},body:JSON.stringify(e),method:"POST"});var Ie=(e,t)=>()=>({path:"/operations",params:t??{},body:JSON.stringify(e),method:"POST"}),Ee=(e,t)=>()=>({path:"/operations",params:t??{},body:JSON.stringify(e),method:"POST"});var Fe=(e,t)=>()=>({path:"/panels",params:t??{},body:JSON.stringify(e),method:"POST"}),Ae=(e,t)=>()=>({path:"/panels",params:t??{},body:JSON.stringify(e),method:"POST"});var we=(e,t)=>()=>({path:"/permissions",params:t??{},body:JSON.stringify(e),method:"POST"}),Ne=(e,t)=>()=>({path:"/permissions",params:t??{},body:JSON.stringify(e),method:"POST"});var ve=(e,t)=>()=>({path:"/policies",params:t??{},body:JSON.stringify(e),method:"POST"}),Ue=(e,t)=>()=>({path:"/policies",params:t??{},body:JSON.stringify(e),method:"POST"});var Je=(e,t)=>()=>({path:"/presets",params:t??{},body:JSON.stringify(e),method:"POST"}),$e=(e,t)=>()=>({path:"/presets",params:t??{},body:JSON.stringify(e),method:"POST"});var ke=e=>()=>({path:"/relations",body:JSON.stringify(e),method:"POST"});var Le=(e,t)=>()=>({path:"/roles",params:t??{},body:JSON.stringify(e),method:"POST"}),Ge=(e,t)=>()=>({path:"/roles",params:t??{},body:JSON.stringify(e),method:"POST"});var Ke=(e,t)=>()=>({path:"/shares",params:t??{},body:JSON.stringify(e),method:"POST"}),We=(e,t)=>()=>({path:"/shares",params:t??{},body:JSON.stringify(e),method:"POST"});var _e=(e,t)=>()=>({path:"/translations",params:t??{},body:JSON.stringify(e),method:"POST"}),He=(e,t)=>()=>({path:"/translations",params:t??{},body:JSON.stringify(e),method:"POST"});var Ve=(e,t)=>()=>({path:"/users",params:t??{},body:JSON.stringify(e),method:"POST"}),Be=(e,t)=>()=>({path:"/users",params:t??{},body:JSON.stringify(e),method:"POST"});var Me=(e,t)=>()=>({path:"/versions",params:t??{},body:JSON.stringify(e),method:"POST"}),qe=(e,t)=>()=>({path:"/versions",params:t??{},body:JSON.stringify(e),method:"POST"});var ze=(e,t)=>()=>({path:"/webhooks",params:t??{},body:JSON.stringify(e),method:"POST"}),Xe=(e,t)=>()=>({path:"/webhooks",params:t??{},body:JSON.stringify(e),method:"POST"});var Ye=e=>()=>({path:`/collections/${e}`,method:"DELETE"});var U=e=>{let t=(o,a=[])=>{if(typeof o=="object"){let s=[];for(let c in o){let n=o[c]??[];if(Array.isArray(n))for(let p of n)s.push(t(p,[...a,c]));else if(typeof n=="object")for(let p of Object.keys(n)){let u=n[p];for(let Q of u)s.push(t(Q,[...a,`${c}:${p}`]))}}return s.flatMap(c=>c)}return[...a,String(o)].join(".")};return e.flatMap(o=>t(o))},$=e=>{let t={};Array.isArray(e.fields)&&e.fields.length>0&&(t.fields=U(e.fields).join(",")),e.filter&&Object.keys(e.filter).length>0&&(t.filter=JSON.stringify(e.filter)),e.search&&(t.search=e.search),"sort"in e&&e.sort&&(t.sort=typeof e.sort=="string"?e.sort:e.sort.join(",")),typeof e.limit=="number"&&e.limit>=-1&&(t.limit=String(e.limit)),typeof e.offset=="number"&&e.offset>=0&&(t.offset=String(e.offset)),typeof e.page=="number"&&e.page>=1&&(t.page=String(e.page)),e.deep&&Object.keys(e.deep).length>0&&(t.deep=JSON.stringify(e.deep)),e.alias&&Object.keys(e.alias).length>0&&(t.alias=JSON.stringify(e.alias)),e.aggregate&&Object.keys(e.aggregate).length>0&&(t.aggregate=JSON.stringify(e.aggregate)),e.groupBy&&e.groupBy.length>0&&(t.groupBy=e.groupBy.join(","));for(let[o,a]of Object.entries(e))o in t||(typeof a=="string"||typeof a=="number"||typeof a=="boolean"?t[o]=String(a):t[o]=JSON.stringify(a));return t};var r=(e,t)=>{if(e.length===0)throw new Error(t)};var f=(e,t)=>{if(j(String(e)))throw new Error(t)};var Ze=e=>()=>{let t={};return Array.isArray(e)?(r(e,"keysOrQuery cannot be empty"),t={keys:e}):(r(Object.keys(e),"keysOrQuery cannot be empty"),t={query:e}),{path:"/comments",body:JSON.stringify(t),method:"DELETE"}},et=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/comments/${e}`,method:"DELETE"});var tt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/dashboards",body:JSON.stringify(e),method:"DELETE"}),rt=e=>()=>(r(e,"Key cannot be empty"),{path:`/dashboards/${e}`,method:"DELETE"});var ot=(e,t)=>()=>(r(e,"Collection cannot be empty"),r(t,"Field cannot be empty"),{path:`/fields/${e}/${t}`,method:"DELETE"});var at=e=>()=>(r(e,"Keys cannot be empty"),{path:"/files",body:JSON.stringify(e),method:"DELETE"}),st=e=>()=>(r(e,"Key cannot be empty"),{path:`/files/${e}`,method:"DELETE"});var mt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/flows",body:JSON.stringify(e),method:"DELETE"}),it=e=>()=>(r(e,"Key cannot be empty"),{path:`/flows/${e}`,method:"DELETE"});var nt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/folders",body:JSON.stringify(e),method:"DELETE"}),ct=e=>()=>(r(e,"Key cannot be empty"),{path:`/folders/${e}`,method:"DELETE"});var pt=(e,t)=>()=>{let o={};return r(String(e),"Collection cannot be empty"),f(e,"Cannot use deleteItems for core collections"),Array.isArray(t)?(r(t,"keysOrQuery cannot be empty"),o={keys:t}):(r(Object.keys(t),"keysOrQuery cannot be empty"),o={query:t}),{path:`/items/${e}`,body:JSON.stringify(o),method:"DELETE"}},ut=(e,t)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use deleteItem for core collections"),r(String(t),"Key cannot be empty"),{path:`/items/${e}/${t}`,method:"DELETE"});var ht=e=>()=>(r(e,"Keys cannot be empty"),{path:"/notifications",body:JSON.stringify(e),method:"DELETE"}),yt=e=>()=>(r(e,"Key cannot be empty"),{path:`/notifications/${e}`,method:"DELETE"});var dt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/operations",body:JSON.stringify(e),method:"DELETE"}),St=e=>()=>(r(e,"Key cannot be empty"),{path:`/operations/${e}`,method:"DELETE"});var lt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/panels",body:JSON.stringify(e),method:"DELETE"}),ft=e=>()=>(r(e,"Key cannot be empty"),{path:`/panels/${e}`,method:"DELETE"});var Qt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/permissions",body:JSON.stringify(e),method:"DELETE"}),xt=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/permissions/${e}`,method:"DELETE"});var Tt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/policies",body:JSON.stringify(e),method:"DELETE"}),Ct=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/policies/${e}`,method:"DELETE"});var gt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/presets",body:JSON.stringify(e),method:"DELETE"}),Rt=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/presets/${e}`,method:"DELETE"});var Dt=(e,t)=>()=>(r(e,"Collection cannot be empty"),r(t,"Field cannot be empty"),{path:`/relations/${e}/${t}`,method:"DELETE"});var bt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/roles",body:JSON.stringify(e),method:"DELETE"}),Ot=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/roles/${e}`,method:"DELETE"});var Pt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/shares",body:JSON.stringify(e),method:"DELETE"}),jt=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/shares/${e}`,method:"DELETE"});var It=e=>()=>(r(e,"Keys cannot be empty"),{path:"/translations",body:JSON.stringify(e),method:"DELETE"}),Et=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/translations/${e}`,method:"DELETE"});var Ft=e=>()=>(r(e,"Keys cannot be empty"),{path:"/users",body:JSON.stringify(e),method:"DELETE"}),At=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/users/${e}`,method:"DELETE"});var wt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/versions",body:JSON.stringify(e),method:"DELETE"}),Nt=e=>()=>(r(e,"Key cannot be empty"),{path:`/versions/${e}`,method:"DELETE"});var vt=e=>()=>(r(e,"Keys cannot be empty"),{path:"/webhooks",body:JSON.stringify(e),method:"DELETE"}),Ut=e=>()=>(r(String(e),"Key cannot be empty"),{path:`/webhooks/${e}`,method:"DELETE"});var Jt=e=>()=>({path:"/activity",params:e??{},method:"GET"}),$t=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/activity/${e}`,params:t??{},method:"GET"});var kt=(e,t)=>()=>{let o=String(e);return r(o,"Collection cannot be empty"),{path:j(o)?`/${o.substring(9)}`:`/items/${o}`,method:"GET",params:{...t.query??{},...t.groupBy?{groupBy:t.groupBy}:{},aggregate:t.aggregate}}};var Lt=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/assets/${e}`,params:t??{},method:"GET",onResponse:o=>o.body}),Gt=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/assets/${e}`,params:t??{},method:"GET",onResponse:o=>o.blob()}),Kt=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/assets/${e}`,params:t??{},method:"GET",onResponse:o=>o.arrayBuffer()});var Wt=()=>()=>({path:"/collections",method:"GET"}),_t=e=>()=>(r(e,"Collection cannot be empty"),{path:`/collections/${e}`,method:"GET"});var Ht=e=>()=>({path:"/comments",params:e??{},method:"GET"}),Vt=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/comments/${e}`,params:t??{},method:"GET"});var Bt=e=>()=>({path:"/dashboards",params:e??{},method:"GET"}),Mt=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/dashboards/${e}`,params:t??{},method:"GET"});var qt=()=>()=>({path:"/extensions/",method:"GET"});var zt=()=>()=>({path:"/fields",method:"GET"}),Xt=e=>()=>(r(e,"Collection cannot be empty"),{path:`/fields/${e}`,method:"GET"}),Yt=(e,t)=>()=>(r(e,"Collection cannot be empty"),r(t,"Field cannot be empty"),{path:`/fields/${e}/${t}`,method:"GET"});var Zt=e=>()=>({path:"/files",params:e??{},method:"GET"}),er=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/files/${e}`,params:t??{},method:"GET"});var tr=e=>()=>({path:"/flows",params:e??{},method:"GET"}),rr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/flows/${e}`,params:t??{},method:"GET"});var or=e=>()=>({path:"/folders",params:e??{},method:"GET"}),ar=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/folders/${e}`,params:t??{},method:"GET"});var sr=(e,t)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use readItems for core collections"),{path:`/items/${e}`,params:t??{},method:"GET"}),mr=(e,t,o)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use readItem for core collections"),r(String(t),"Key cannot be empty"),{path:`/items/${e}/${t}`,params:o??{},method:"GET"});var ir=e=>()=>({path:"/notifications",params:e??{},method:"GET"}),nr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/notifications/${e}`,params:t??{},method:"GET"});var cr=e=>()=>({path:"/operations",params:e??{},method:"GET"}),pr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/operations/${e}`,params:t??{},method:"GET"});var ur=e=>()=>({path:"/panels",params:e??{},method:"GET"}),hr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/panels/${e}`,params:t??{},method:"GET"});var yr=e=>()=>({path:"/permissions",params:e??{},method:"GET"}),dr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/permissions/${e}`,params:t??{},method:"GET"}),Sr=(e,t)=>()=>(r(String(e),"Collection cannot be empty"),{path:`/permissions/me/${t?`${e}/${t}`:`${e}`}`,method:"GET"}),lr=()=>()=>({path:"/permissions/me",method:"GET"});var fr=e=>()=>({path:"/policies",params:e??{},method:"GET"}),Qr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/policies/${e}`,params:t??{},method:"GET"}),xr=()=>()=>({path:"/policies/me/globals",method:"GET"});var Tr=e=>()=>({path:"/presets",params:e??{},method:"GET"}),Cr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/presets/${e}`,params:t??{},method:"GET"});var gr=()=>()=>({path:"/relations",method:"GET"}),Rr=e=>()=>({path:`/relations/${e}`,method:"GET"}),Dr=(e,t)=>()=>(r(e,"Collection cannot be empty"),r(t,"Field cannot be empty"),{path:`/relations/${e}/${t}`,method:"GET"});var br=e=>()=>({path:"/revisions",params:e??{},method:"GET"}),Or=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/revisions/${e}`,params:t??{},method:"GET"});var Pr=e=>()=>({path:"/roles",params:e??{},method:"GET"}),jr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/roles/${e}`,params:t??{},method:"GET"}),Ir=e=>()=>({path:"/roles/me",params:e??{},method:"GET"});var Er=e=>()=>({path:"/settings",params:e??{},method:"GET"});var Fr=e=>()=>({path:"/shares",params:e??{},method:"GET"}),Ar=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/shares/${e}`,params:t??{},method:"GET"});var wr=(e,t)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use readSingleton for core collections"),{path:`/items/${e}`,params:t??{},method:"GET"});var Nr=e=>()=>({path:"/translations",params:e??{},method:"GET"}),vr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/translations/${e}`,params:t??{},method:"GET"});var Ur=e=>()=>({path:"/users",params:e??{},method:"GET"}),Jr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/users/${e}`,params:t??{},method:"GET"}),$r=e=>()=>({path:"/users/me",params:e??{},method:"GET"});var kr=e=>()=>({path:"/versions",params:e??{},method:"GET"}),Lr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/versions/${e}`,params:t??{},method:"GET"});var Gr=e=>()=>({path:"/webhooks",params:e??{},method:"GET"}),Kr=(e,t)=>()=>(r(String(e),"Key cannot be empty"),{path:`/webhooks/${e}`,params:t??{},method:"GET"});var Wr=e=>()=>({method:"POST",path:"/schema/apply",body:JSON.stringify(e)});var _r=(e,t=!1)=>()=>({method:"POST",path:"/schema/diff",params:t?{force:t}:{},body:JSON.stringify(e)});var Hr=()=>()=>({method:"GET",path:"/schema/snapshot"});var Vr=(e="item")=>()=>({method:"GET",path:e==="item"?"/server/specs/graphql":"/server/specs/graphql/system"});var Br=()=>()=>({method:"GET",path:"/server/health"});var Mr=()=>()=>({method:"GET",path:"/server/info"});var qr=()=>()=>({method:"GET",path:"/server/specs/oas"});var zr=()=>()=>({method:"GET",path:"/server/ping"});var Xr=(e,t,o)=>()=>(r(e,"Collection cannot be empty"),{path:`/collections/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"}),Yr=(e,t)=>()=>({path:"/collections",params:t??{},body:JSON.stringify(e),method:"PATCH"});var Zr=(e,t,o)=>()=>{let a={};return Array.isArray(e)?(r(e,"keysOrQuery cannot be empty"),a={keys:e}):(r(Object.keys(e),"keysOrQuery cannot be empty"),a={query:e}),a.data=t,{path:"/comments",params:o??{},body:JSON.stringify(a),method:"PATCH"}},eo=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/comments/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var to=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/dashboards",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),ro=(e,t)=>()=>({path:"/dashboards",params:t??{},body:JSON.stringify(e),method:"PATCH"}),oo=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/dashboards/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var ao=(e,t,o)=>()=>(e!==null&&r(e,"Bundle cannot be an empty string"),r(t,"Name cannot be empty"),{path:e?`/extensions/${e}/${t}`:`/extensions/${t}`,params:{},body:JSON.stringify(o),method:"PATCH"});var so=(e,t,o,a)=>()=>(r(e,"Keys cannot be empty"),r(t,"Field cannot be empty"),{path:`/fields/${e}/${t}`,params:a??{},body:JSON.stringify(o),method:"PATCH"});var mo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/files",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),io=(e,t)=>()=>({path:"/files",params:t??{},body:JSON.stringify(e),method:"PATCH"}),no=(e,t,o)=>()=>(r(e,"Key cannot be empty"),t instanceof FormData?{path:`/files/${e}`,params:o??{},body:t,method:"PATCH",headers:{"Content-Type":"multipart/form-data"}}:{path:`/files/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var co=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/flows",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),po=(e,t)=>()=>({path:"/flows",params:t??{},body:JSON.stringify(e),method:"PATCH"}),uo=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/flows/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var ho=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/folders",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),yo=(e,t)=>()=>({path:"/folders",params:t??{},body:JSON.stringify(e),method:"PATCH"}),So=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/folders/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var lo=(e,t,o,a)=>()=>{let s={};return r(String(e),"Collection cannot be empty"),f(e,"Cannot use updateItems for core collections"),Array.isArray(t)?(r(t,"keysOrQuery cannot be empty"),s={keys:t}):(r(Object.keys(t),"keysOrQuery cannot be empty"),s={query:t}),s.data=o,{path:`/items/${e}`,params:a??{},body:JSON.stringify(s),method:"PATCH"}},fo=(e,t,o)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use updateItems for core collections"),{path:`/items/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"}),Qo=(e,t,o,a)=>()=>(r(String(t),"Key cannot be empty"),r(String(e),"Collection cannot be empty"),f(e,"Cannot use updateItem for core collections"),{path:`/items/${e}/${t}`,params:a??{},body:JSON.stringify(o),method:"PATCH"});var xo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/notifications",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),To=(e,t)=>()=>({path:"/notifications",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Co=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/notifications/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var go=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/operations",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Ro=(e,t)=>()=>({path:"/operations",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Do=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/operations/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var bo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/panels",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Oo=(e,t)=>()=>({path:"/panels",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Po=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/panels/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var jo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/permissions",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Io=(e,t)=>()=>({path:"/permissions",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Eo=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/permissions/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var Fo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/policies",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Ao=(e,t)=>()=>({path:"/policies",params:t??{},body:JSON.stringify(e),method:"PATCH"}),wo=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/policies/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var No=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/presets",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),vo=(e,t)=>()=>({path:"/presets",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Uo=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/presets/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var Jo=(e,t,o,a)=>()=>(r(e,"Collection cannot be empty"),r(t,"Field cannot be empty"),{path:`/relations/${e}/${t}`,params:a??{},body:JSON.stringify(o),method:"PATCH"});var $o=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/roles",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),ko=(e,t)=>()=>({path:"/roles",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Lo=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/roles/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var Go=(e,t)=>()=>({path:"/settings",params:t??{},body:JSON.stringify(e),method:"PATCH"});var Ko=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/shares",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Wo=(e,t)=>()=>({path:"/shares",params:t??{},body:JSON.stringify(e),method:"PATCH"}),_o=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/shares/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var Ho=(e,t,o)=>()=>(r(String(e),"Collection cannot be empty"),f(e,"Cannot use updateSingleton for core collections"),{path:`/items/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var Vo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/translations",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),Bo=(e,t)=>()=>({path:"/translations",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Mo=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/translations/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var qo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/users",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),zo=(e,t)=>()=>({path:"/users",params:t??{},body:JSON.stringify(e),method:"PATCH"}),Xo=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/users/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"}),Yo=(e,t)=>()=>({path:"/users/me",params:t??{},body:JSON.stringify(e),method:"PATCH"});var Zo=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/versions",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),ea=(e,t)=>()=>({path:"/versions",params:t??{},body:JSON.stringify(e),method:"PATCH"}),ta=(e,t,o)=>()=>(r(e,"Key cannot be empty"),{path:`/versions/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var ra=(e,t,o)=>()=>(r(e,"Keys cannot be empty"),{path:"/webhooks",params:o??{},body:JSON.stringify({keys:e,data:t}),method:"PATCH"}),oa=(e,t,o)=>()=>(r(String(e),"Key cannot be empty"),{path:`/webhooks/${e}`,params:o??{},body:JSON.stringify(t),method:"PATCH"});var aa=()=>(e=!1)=>({method:"POST",path:`/utils/cache/clear${e?"?system":""}`});var sa=(e,t,o,a)=>()=>({method:"POST",path:`/utils/export/${e}`,body:JSON.stringify({format:t,query:o,file:a})});var ma=(e,t,o)=>()=>e==="GET"?{path:`/flows/trigger/${t}`,params:o??{},method:"GET"}:{path:`/flows/trigger/${t}`,body:JSON.stringify(o??{}),method:"POST"};var ia=e=>()=>({method:"POST",path:"/utils/hash/generate",body:JSON.stringify({string:e})}),na=(e,t)=>()=>({method:"POST",path:"/utils/hash/verify",body:JSON.stringify({string:e,hash:t})});var ca=(e,t)=>()=>({path:`/utils/import/${e}`,method:"POST",body:t,headers:{"Content-Type":"multipart/form-data"}});var pa=(e,t,o="cookie")=>()=>({path:"/shares/auth",method:"POST",body:JSON.stringify({share:e,password:t,mode:o})}),ua=(e,t)=>()=>({path:"/shares/invite",method:"POST",body:JSON.stringify({share:e,emails:t})}),ha=e=>()=>({path:`/shares/info/${e}`,method:"GET"});var ya=(e,t,o)=>()=>({method:"POST",path:`/utils/sort/${e}`,body:JSON.stringify({item:t,to:o})});var da=(e,t,o)=>()=>({path:"/users/invite",method:"POST",body:JSON.stringify({email:e,role:t,...o?{invite_url:o}:{}})}),Sa=(e,t)=>()=>({path:"/users/invite/accept",method:"POST",body:JSON.stringify({token:e,password:t})}),la=(e,t,o={})=>()=>({path:"/users/register",method:"POST",body:JSON.stringify({email:e,password:t,...o})}),fa=e=>()=>({path:"/users/register/verify-email",params:{token:e},method:"GET"}),Qa=e=>()=>({path:"/users/me/tfa/generate",method:"POST",body:JSON.stringify({password:e})}),xa=(e,t)=>()=>({path:"/users/me/tfa/enable",method:"POST",body:JSON.stringify({secret:e,otp:t})}),Ta=e=>()=>({path:"/users/me/tfa/disable",method:"POST",body:JSON.stringify({otp:e})});var Ca=(e,t)=>()=>(r(e,"ID cannot be empty"),{path:`/versions/${e}/save`,method:"POST",body:JSON.stringify(t)}),ga=e=>()=>(r(e,"ID cannot be empty"),{path:`/versions/${e}/compare`,method:"GET"}),Ra=(e,t,o)=>()=>(r(e,"ID cannot be empty"),{path:`/versions/${e}/promote`,method:"POST",body:JSON.stringify(o?{mainHash:t,fields:o}:{mainHash:t})});var Da=e=>()=>({method:"GET",path:"/utils/random/string",params:e!==void 0?{length:e}:{}});var ba={},Oa=(e={})=>t=>{let o={...ba,...e};return{async request(a){let s=a();if(s.headers||(s.headers={}),"Content-Type"in s.headers?s.headers["Content-Type"]==="multipart/form-data"&&delete s.headers["Content-Type"]:s.headers["Content-Type"]="application/json","getToken"in this&&!("Authorization"in s.headers)){let u=await this.getToken();u&&(s.headers.Authorization=`Bearer ${u}`)}let c=R(t.url,s.path,s.params),n={method:s.method??"GET",headers:s.headers??{}};"credentials"in o&&(n.credentials=o.credentials),s.body&&(n.body=s.body),s.onRequest&&(n=await s.onRequest(n)),o.onRequest&&(n=await o.onRequest(n));let p=await D(c.toString(),n,t.globals.fetch);return"onResponse"in s&&(p=await s.onResponse(p,n)),"onResponse"in e&&(p=await e.onResponse(p,n)),p}}};function Pa(e,t){return()=>{let o=e();return typeof t=="function"?o.onRequest=t:o.onRequest=a=>({...a,...t}),o}}function ja(e){return()=>{let t=e();return t.method==="GET"&&t.params&&(t.method="SEARCH",t.body=JSON.stringify({query:{...t.params,fields:U(t.params.fields??[])}}),delete t.params),t}}function Ia(e,t){return()=>{let o=t();return e&&(o.headers||(o.headers={}),o.headers.Authorization=`Bearer ${e}`),o}}function Ea(e){return()=>e}function Fa(e){return typeof e=="object"&&e!==null&&"errors"in e&&Array.isArray(e.errors)&&"message"in e.errors[0]&&"extensions"in e.errors[0]&&"code"in e.errors[0].extensions}0&&(module.exports={acceptUserInvite,aggregate,auth,authenticateShare,authentication,clearCache,compareContentVersion,createCollection,createComment,createComments,createContentVersion,createContentVersions,createDashboard,createDashboards,createDirectus,createField,createFlow,createFlows,createFolder,createFolders,createItem,createItems,createNotification,createNotifications,createOperation,createOperations,createPanel,createPanels,createPermission,createPermissions,createPolicies,createPolicy,createPreset,createPresets,createRelation,createRole,createRoles,createShare,createShares,createTranslation,createTranslations,createUser,createUsers,createWebhook,createWebhooks,customEndpoint,deleteCollection,deleteComment,deleteComments,deleteContentVersion,deleteContentVersions,deleteDashboard,deleteDashboards,deleteField,deleteFile,deleteFiles,deleteFlow,deleteFlows,deleteFolder,deleteFolders,deleteItem,deleteItems,deleteNotification,deleteNotifications,deleteOperation,deleteOperations,deletePanel,deletePanels,deletePermission,deletePermissions,deletePolicies,deletePolicy,deletePreset,deletePresets,deleteRelation,deleteRole,deleteRoles,deleteShare,deleteShares,deleteTranslation,deleteTranslations,deleteUser,deleteUsers,deleteWebhook,deleteWebhooks,disableTwoFactor,enableTwoFactor,formatFields,generateHash,generateTwoFactorSecret,generateUid,getAuthEndpoint,graphql,importFile,inviteShare,inviteUser,isDirectusError,login,logout,memoryStorage,messageCallback,passwordRequest,passwordReset,pong,promoteContentVersion,queryToParams,randomString,readActivities,readActivity,readAssetArrayBuffer,readAssetBlob,readAssetRaw,readCollection,readCollections,readComment,readComments,readContentVersion,readContentVersions,readDashboard,readDashboards,readExtensions,readField,readFields,readFieldsByCollection,readFile,readFiles,readFlow,readFlows,readFolder,readFolders,readGraphqlSdl,readItem,readItemPermissions,readItems,readMe,readNotification,readNotifications,readOpenApiSpec,readOperation,readOperations,readPanel,readPanels,readPermission,readPermissions,readPolicies,readPolicy,readPolicyGlobals,readPreset,readPresets,readProviders,readRelation,readRelationByCollection,readRelations,readRevision,readRevisions,readRole,readRoles,readRolesMe,readSettings,readShare,readShareInfo,readShares,readSingleton,readTranslation,readTranslations,readUser,readUserPermissions,readUsers,readWebhook,readWebhooks,realtime,refresh,registerUser,registerUserVerify,rest,saveToContentVersion,schemaApply,schemaDiff,schemaSnapshot,serverHealth,serverInfo,serverPing,sleep,staticToken,throwIfCoreCollection,throwIfEmpty,triggerFlow,updateCollection,updateCollectionsBatch,updateComment,updateComments,updateContentVersion,updateContentVersions,updateContentVersionsBatch,updateDashboard,updateDashboards,updateDashboardsBatch,updateExtension,updateField,updateFile,updateFiles,updateFilesBatch,updateFlow,updateFlows,updateFlowsBatch,updateFolder,updateFolders,updateFoldersBatch,updateItem,updateItems,updateItemsBatch,updateMe,updateNotification,updateNotifications,updateNotificationsBatch,updateOperation,updateOperations,updateOperationsBatch,updatePanel,updatePanels,updatePanelsBatch,updatePermission,updatePermissions,updatePermissionsBatch,updatePolicies,updatePoliciesBatch,updatePolicy,updatePreset,updatePresets,updatePresetsBatch,updateRelation,updateRole,updateRoles,updateRolesBatch,updateSettings,updateShare,updateShares,updateSharesBatch,updateSingleton,updateTranslation,updateTranslations,updateTranslationsBatch,updateUser,updateUsers,updateUsersBatch,updateWebhook,updateWebhooks,uploadFiles,utilitySort,utilsExport,utilsImport,verifyHash,withOptions,withSearch,withToken});
//# sourceMappingURL=index.cjs.map