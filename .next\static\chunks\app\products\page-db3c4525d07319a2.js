(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{1057:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(5155),l=s(2115),c=s(3152),i=s(4295),r=s(3619),n=s(4951),d=s(9655);function o(){let[e,t]=(0,l.useState)([]),[s,o]=(0,l.useState)([]),[m,x]=(0,l.useState)(null),[h,u]=(0,l.useState)(1),[p,g]=(0,l.useState)(1),[j,b]=(0,l.useState)(!0),{config:f}=(0,n.U)();return((0,l.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([(0,c.d$)(),(0,c.ql)()]);t(e),o(s)}catch(e){}finally{b(!1)}})()},[]),(0,l.useEffect)(()=>{(async()=>{b(!0);try{let e=await (0,c.Ct)(m,12,h);t(e.data);let s=e.meta.total_pages||1;g(s)}catch(e){t([]),g(1)}finally{b(!1)}})()},[m,h]),(0,l.useEffect)(()=>{if(f){document.title="产品展示-".concat(f.title);let e=document.querySelector('meta[name="description"]');if(e)e.setAttribute("content",f.description||"浏览我们的全系列产品");else{let e=document.createElement("meta");e.name="description",e.content=f.description||"浏览我们的全系列产品",document.head.appendChild(e)}}},[f]),j)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"产品展示"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"浏览我们的全系列产品"})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(r.A,{categories:s,selectedCategory:m,onCategorySelect:x,title:"产品分类",allItemsLabel:"全部产品"}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:w-1/4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 sticky top-24",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"产品分类"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("button",{onClick:()=>x(null),className:"w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ".concat(null===m?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:"全部产品"})}),s.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("button",{onClick:()=>x(e.id),className:"w-full text-left px-3 py-2 rounded-md transition-colors duration-200 ".concat(m===e.id?"bg-blue-100 text-blue-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:e.name})},e.id))]})]})}),(0,a.jsx)("div",{className:"lg:w-3/4",children:0===e.length?(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"暂无产品"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsx)(i.A,{product:e},e.id))}),(0,a.jsx)(d.A,{currentPage:h,totalPages:p,onPageChange:e=>{u(e),window.scrollTo({top:0,behavior:"smooth"})}})]})})]})]})]})}},4295:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(5155),l=s(6766),c=s(6874),i=s.n(c);let r=e=>{let{product:t}=e,s=t.main_image?"".concat("http://172.25.165.28:8055","/assets/").concat(t.main_image,"?updated=").concat(t.updated_at||t.created_at):"/placeholder-product.svg";return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsx)(i(),{href:"/products/".concat(t.id),className:"block",children:(0,a.jsx)("div",{className:"relative h-48 cursor-pointer",children:(0,a.jsx)(l.default,{src:s,alt:t.name,fill:!0,className:"object-cover transition-transform duration-300 hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)(i(),{href:"/products/".concat(t.id),children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors duration-200 cursor-pointer",children:t.name})}),t.short_description&&(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-3",children:t.short_description}),t.price&&(0,a.jsxs)("p",{className:"text-blue-600 font-bold text-lg mb-3",children:["\xa5",parseFloat(t.price).toLocaleString()]})]})]})}},5667:(e,t,s)=>{Promise.resolve().then(s.bind(s,1057))}},e=>{e.O(0,[453,874,766,407,441,964,358],()=>e(e.s=5667)),_N_E=e.O()}]);